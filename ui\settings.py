# -*- coding: utf-8 -*-
"""
قسم الإعدادات المتقدم
Advanced Settings Section
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QLabel, QLineEdit, QPushButton, QCheckBox, QSpinBox,
                            QComboBox, QTextEdit, QGroupBox, QGridLayout,
                            QMessageBox, QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QScrollArea, QSlider, QColorDialog)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

# إضافة المسار للأنظمة المتقدمة
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

try:
    from config import AppConfig, DatabaseConfig, SecurityConfig, BackupConfig, PerformanceConfig
    from logging_config import smart_logger, log_info
    BASIC_SYSTEMS_AVAILABLE = True
except ImportError:
    BASIC_SYSTEMS_AVAILABLE = False

try:
    from backup_manager import backup_manager
    from permissions_manager import permissions_manager
    from security_manager import security_manager
    from performance_monitor import performance_monitor
    from system_initializer import get_systems_status
    ADVANCED_SYSTEMS_AVAILABLE = True
except ImportError:
    ADVANCED_SYSTEMS_AVAILABLE = False

class SystemStatusThread(QThread):
    """خيط لمراقبة حالة الأنظمة"""
    status_updated = pyqtSignal(dict)
    
    def run(self):
        while True:
            if ADVANCED_SYSTEMS_AVAILABLE:
                try:
                    status = get_systems_status()
                    self.status_updated.emit(status)
                except Exception as e:
                    print(f"خطأ في مراقبة حالة الأنظمة: {e}")
            
            self.msleep(5000)  # تحديث كل 5 ثوان

class SettingsWidget(QWidget):
    """واجهة الإعدادات المتقدمة"""
    
    def __init__(self, session=None):
        super().__init__()
        self.session = session
        self.status_thread = None
        self.init_ui()
        
        if ADVANCED_SYSTEMS_AVAILABLE:
            self.start_status_monitoring()
        elif BASIC_SYSTEMS_AVAILABLE:
            # عرض معلومات الأنظمة الأساسية فقط
            self.show_basic_info()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي مطابق تماماً لباقي البرنامج
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية
        layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي مطابق للتقارير
        title_label = QLabel("⚙️ إدارة الإعدادات المتطورة - نظام شامل ومتقدم لإدارة الإعدادات مع أدوات احترافية للتحكم والمراقبة والصيانة")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        layout.addWidget(title_label)
        
        if not BASIC_SYSTEMS_AVAILABLE:
            # رسالة عدم توفر الأنظمة الأساسية
            warning_label = QLabel("❌ الأنظمة الأساسية غير متاحة\n💡 تأكد من وجود ملفات config.py و logging_config.py")
            warning_label.setAlignment(Qt.AlignCenter)
            warning_label.setStyleSheet("""
                QLabel {
                    color: #ef4444;
                    background: #fef2f2;
                    padding: 20px;
                    border-radius: 8px;
                    font-size: 14px;
                }
            """)
            layout.addWidget(warning_label)
            return

        if not ADVANCED_SYSTEMS_AVAILABLE:
            # رسالة عدم توفر الأنظمة المتقدمة
            warning_label = QLabel("⚠️ بعض الأنظمة المتقدمة غير متاحة\n💡 لتفعيل جميع الميزات: pip install schedule cryptography bcrypt")
            warning_label.setAlignment(Qt.AlignCenter)
            warning_label.setStyleSheet("""
                QLabel {
                    color: #f59e0b;
                    background: #fef3c7;
                    padding: 15px;
                    border-radius: 8px;
                    font-size: 12px;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(warning_label)
        
        # إنشاء تبويبات للإعدادات المختلفة مع تصميم محسن ومطابق لباقي البرنامج
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 136px;
                max-width: 136px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease-in-out;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 136px;
                max-width: 136px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
                transform: scale(1.05);
            }
        """)
        
        # تبويب حالة الأنظمة
        self.create_system_status_tab()

        # تبويب إعدادات الأمان
        self.create_security_tab()

        # تبويب النسخ الاحتياطي
        self.create_backup_tab()

        # تبويب مراقبة الأداء
        self.create_performance_tab()

        # تبويب السجلات
        self.create_logs_tab()

        # تبويب الإعدادات العامة
        self.create_general_tab()

        layout.addWidget(self.tabs)

        # تطبيق العرض التكيفي بعد إضافة جميع التبويبات
        self.apply_adaptive_width()

    def apply_adaptive_width(self):
        """تطبيق العرض التكيفي التلقائي للتبويبات"""
        try:
            # حساب العرض المتاح
            available_width = self.width() if self.width() > 0 else 1200  # عرض افتراضي
            tab_count = self.tabs.count()

            if tab_count > 0:
                # حساب العرض المناسب لكل تبويب
                # ترك مساحة للهوامش والحدود والمسافات
                margin_space = 100  # مساحة للهوامش والحدود
                padding_space = tab_count * 20  # مساحة للحشو والمسافات بين التبويبات

                # حساب العرض المتاح للتبويبات
                usable_width = available_width - margin_space - padding_space
                tab_width = max(100, min(250, usable_width // tab_count))

                # تأكد من أن العرض منطقي
                if tab_width < 100:
                    tab_width = 100
                elif tab_width > 250:
                    tab_width = 250

                # إنشاء تصميم جديد مع العرض التكيفي
                adaptive_style = f"""
            QTabWidget::pane {{
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }}
            QTabBar::tab {{
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: {tab_width}px;
                max-width: {tab_width}px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease-in-out;
            }}
            QTabBar::tab:selected {{
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: {tab_width}px;
                max-width: {tab_width}px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }}
            QTabBar::tab:hover {{
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
                transform: scale(1.05);
            }}
        """

                self.tabs.setStyleSheet(adaptive_style)

                print(f"🔧 تم تطبيق العرض التكيفي: {tab_width}px لكل تبويب")

        except Exception as e:
            print(f"⚠️ خطأ في العرض التكيفي: {e}")

    def resizeEvent(self, event):
        """إعادة حساب العرض عند تغيير حجم النافذة"""
        super().resizeEvent(event)
        if hasattr(self, 'tabs'):
            self.apply_adaptive_width()

    def create_system_status_tab(self):
        """تبويب حالة الأنظمة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # حالة الأنظمة
        status_group = QGroupBox("📊 حالة الأنظمة المتقدمة")
        status_layout = QGridLayout(status_group)
        
        self.status_labels = {}
        systems = [
            ("config", "الإعدادات المركزية"),
            ("logging", "نظام السجلات"),
            ("security", "نظام الأمان"),
            ("permissions", "نظام الصلاحيات"),
            ("backup", "النسخ الاحتياطي"),
            ("performance", "مراقبة الأداء")
        ]
        
        for i, (key, name) in enumerate(systems):
            label = QLabel(f"🔧 {name}:")
            status_label = QLabel("⏳ جاري التحقق...")
            status_label.setStyleSheet("color: #f59e0b;")
            
            status_layout.addWidget(label, i, 0)
            status_layout.addWidget(status_label, i, 1)
            
            self.status_labels[key] = status_label
        
        layout.addWidget(status_group)
        
        # أزرار التحكم
        controls_group = QGroupBox("🎛️ أدوات التحكم")
        controls_layout = QHBoxLayout(controls_group)
        
        refresh_btn = QPushButton("🔄 تحديث الحالة")
        refresh_btn.clicked.connect(self.refresh_system_status)
        controls_layout.addWidget(refresh_btn)

        test_btn = QPushButton("🧪 اختبار الأنظمة")
        test_btn.clicked.connect(self.test_systems)
        controls_layout.addWidget(test_btn)

        restart_btn = QPushButton("🔄 إعادة تشغيل الأنظمة")
        restart_btn.clicked.connect(self.restart_systems)
        controls_layout.addWidget(restart_btn)

        cleanup_btn = QPushButton("🧹 تنظيف النظام")
        cleanup_btn.clicked.connect(self.cleanup_system)
        controls_layout.addWidget(cleanup_btn)
        
        layout.addWidget(controls_group)
        
        self.tabs.addTab(tab, "📊 حالة الأنظمة")
    
    def create_security_tab(self):
        """تبويب إعدادات الأمان"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات كلمات المرور
        password_group = QGroupBox("🔐 إعدادات كلمات المرور")
        password_layout = QGridLayout(password_group)
        
        password_layout.addWidget(QLabel("الحد الأدنى لطول كلمة المرور:"), 0, 0)
        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(4, 20)
        self.min_password_length.setValue(SecurityConfig.PASSWORD_MIN_LENGTH)
        password_layout.addWidget(self.min_password_length, 0, 1)
        
        password_layout.addWidget(QLabel("الحد الأقصى لمحاولات تسجيل الدخول:"), 1, 0)
        self.max_login_attempts = QSpinBox()
        self.max_login_attempts.setRange(3, 10)
        self.max_login_attempts.setValue(SecurityConfig.MAX_LOGIN_ATTEMPTS)
        password_layout.addWidget(self.max_login_attempts, 1, 1)
        
        layout.addWidget(password_group)
        
        # إعدادات الجلسة
        session_group = QGroupBox("⏰ إعدادات الجلسة")
        session_layout = QGridLayout(session_group)
        
        session_layout.addWidget(QLabel("مهلة انتهاء الجلسة (ثانية):"), 0, 0)
        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(300, 7200)
        self.session_timeout.setValue(SecurityConfig.SESSION_TIMEOUT)
        session_layout.addWidget(self.session_timeout, 0, 1)
        
        layout.addWidget(session_group)
        
        # أزرار الأمان
        security_controls = QGroupBox("🛡️ أدوات الأمان")
        security_layout = QHBoxLayout(security_controls)
        
        change_password_btn = QPushButton("🔑 تغيير كلمة المرور")
        change_password_btn.clicked.connect(self.change_password)
        security_layout.addWidget(change_password_btn)
        
        security_report_btn = QPushButton("📋 تقرير الأمان")
        security_report_btn.clicked.connect(self.show_security_report)
        security_layout.addWidget(security_report_btn)
        
        layout.addWidget(security_controls)
        
        self.tabs.addTab(tab, "🔐 الأمان")
    
    def create_backup_tab(self):
        """تبويب النسخ الاحتياطي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات النسخ الاحتياطي
        backup_settings = QGroupBox("💾 إعدادات النسخ الاحتياطي")
        backup_layout = QGridLayout(backup_settings)
        
        self.auto_backup_enabled = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup_enabled.setChecked(BackupConfig.AUTO_BACKUP_ENABLED)
        backup_layout.addWidget(self.auto_backup_enabled, 0, 0, 1, 2)
        
        backup_layout.addWidget(QLabel("فترة النسخ الاحتياطي (ساعات):"), 1, 0)
        self.backup_interval = QSpinBox()
        self.backup_interval.setRange(1, 24)
        self.backup_interval.setValue(BackupConfig.BACKUP_INTERVAL_HOURS)
        backup_layout.addWidget(self.backup_interval, 1, 1)
        
        backup_layout.addWidget(QLabel("عدد النسخ المحفوظة:"), 2, 0)
        self.max_backups = QSpinBox()
        self.max_backups.setRange(5, 100)
        self.max_backups.setValue(BackupConfig.MAX_BACKUP_FILES)
        backup_layout.addWidget(self.max_backups, 2, 1)
        
        self.backup_compression = QCheckBox("ضغط النسخ الاحتياطية")
        self.backup_compression.setChecked(BackupConfig.BACKUP_COMPRESSION)
        backup_layout.addWidget(self.backup_compression, 3, 0, 1, 2)
        
        layout.addWidget(backup_settings)
        
        # أدوات النسخ الاحتياطي
        backup_controls = QGroupBox("🎛️ أدوات النسخ الاحتياطي")
        backup_controls_layout = QHBoxLayout(backup_controls)
        
        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.clicked.connect(self.create_backup)
        backup_controls_layout.addWidget(create_backup_btn)
        
        list_backups_btn = QPushButton("📋 قائمة النسخ")
        list_backups_btn.clicked.connect(self.list_backups)
        backup_controls_layout.addWidget(list_backups_btn)
        
        layout.addWidget(backup_controls)
        
        self.tabs.addTab(tab, "💾 النسخ الاحتياطي")
    
    def create_performance_tab(self):
        """تبويب مراقبة الأداء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # إعدادات الأداء
        perf_settings = QGroupBox("📊 إعدادات مراقبة الأداء")
        perf_layout = QGridLayout(perf_settings)
        
        self.performance_monitoring = QCheckBox("تفعيل مراقبة الأداء")
        self.performance_monitoring.setChecked(PerformanceConfig.PERFORMANCE_MONITORING_ENABLED)
        perf_layout.addWidget(self.performance_monitoring, 0, 0, 1, 2)
        
        perf_layout.addWidget(QLabel("الحد الأقصى لاستخدام الذاكرة (MB):"), 1, 0)
        self.max_memory = QSpinBox()
        self.max_memory.setRange(256, 2048)
        self.max_memory.setValue(PerformanceConfig.MAX_MEMORY_USAGE_MB)
        perf_layout.addWidget(self.max_memory, 1, 1)
        
        perf_layout.addWidget(QLabel("الحد الأقصى لوقت الاستعلام (ثانية):"), 2, 0)
        self.max_query_time = QSpinBox()
        self.max_query_time.setRange(1, 30)
        self.max_query_time.setValue(PerformanceConfig.MAX_QUERY_TIME_SECONDS)
        perf_layout.addWidget(self.max_query_time, 2, 1)
        
        layout.addWidget(perf_settings)
        
        # أدوات الأداء
        perf_controls = QGroupBox("🎛️ أدوات الأداء")
        perf_controls_layout = QHBoxLayout(perf_controls)
        
        performance_report_btn = QPushButton("📊 تقرير الأداء")
        performance_report_btn.clicked.connect(self.show_performance_report)
        perf_controls_layout.addWidget(performance_report_btn)
        
        optimize_btn = QPushButton("⚡ تحسين الأداء")
        optimize_btn.clicked.connect(self.optimize_performance)
        perf_controls_layout.addWidget(optimize_btn)
        
        layout.addWidget(perf_controls)
        
        self.tabs.addTab(tab, "📊 الأداء")
    
    def create_logs_tab(self):
        """تبويب السجلات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أدوات السجلات
        logs_controls = QGroupBox("📝 أدوات السجلات")
        logs_layout = QHBoxLayout(logs_controls)
        
        view_logs_btn = QPushButton("👁️ عرض السجلات")
        view_logs_btn.clicked.connect(self.view_logs)
        logs_layout.addWidget(view_logs_btn)
        
        clear_logs_btn = QPushButton("🗑️ مسح السجلات")
        clear_logs_btn.clicked.connect(self.clear_logs)
        logs_layout.addWidget(clear_logs_btn)
        
        export_logs_btn = QPushButton("📤 تصدير السجلات")
        export_logs_btn.clicked.connect(self.export_logs)
        logs_layout.addWidget(export_logs_btn)
        
        layout.addWidget(logs_controls)
        
        # عرض السجلات الأخيرة
        recent_logs_group = QGroupBox("📋 السجلات الأخيرة")
        recent_logs_layout = QVBoxLayout(recent_logs_group)
        
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setMaximumHeight(200)
        self.logs_text.setStyleSheet("""
            QTextEdit {
                background: #1e293b;
                color: #e2e8f0;
                font-family: 'Courier New';
                font-size: 10px;
            }
        """)
        recent_logs_layout.addWidget(self.logs_text)
        
        layout.addWidget(recent_logs_group)
        
        self.tabs.addTab(tab, "📝 السجلات")
    
    def create_general_tab(self):
        """تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # معلومات التطبيق
        app_info = QGroupBox("ℹ️ معلومات التطبيق")
        app_layout = QGridLayout(app_info)
        
        app_layout.addWidget(QLabel("اسم التطبيق:"), 0, 0)
        app_layout.addWidget(QLabel(AppConfig.APP_NAME), 0, 1)
        
        app_layout.addWidget(QLabel("الإصدار:"), 1, 0)
        app_layout.addWidget(QLabel(AppConfig.APP_VERSION), 1, 1)
        
        app_layout.addWidget(QLabel("المطور:"), 2, 0)
        app_layout.addWidget(QLabel(AppConfig.APP_AUTHOR), 2, 1)
        
        layout.addWidget(app_info)
        
        # أزرار الإجراءات
        actions_group = QGroupBox("⚡ إجراءات سريعة")
        actions_layout = QHBoxLayout(actions_group)
        
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_settings)
        actions_layout.addWidget(save_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.clicked.connect(self.reset_settings)
        actions_layout.addWidget(reset_settings_btn)

        update_btn = QPushButton("🔄 تحديث التطبيق")
        update_btn.clicked.connect(self.check_for_updates)
        actions_layout.addWidget(update_btn)
        
        layout.addWidget(actions_group)
        
        self.tabs.addTab(tab, "⚙️ عام")

    def show_basic_info(self):
        """عرض معلومات الأنظمة الأساسية"""
        if hasattr(self, 'status_labels'):
            # تحديث حالة الأنظمة الأساسية
            if BASIC_SYSTEMS_AVAILABLE:
                self.status_labels.get('config', QLabel()).setText("✅ يعمل")
                self.status_labels.get('config', QLabel()).setStyleSheet("color: #10b981;")
                self.status_labels.get('logging', QLabel()).setText("✅ يعمل")
                self.status_labels.get('logging', QLabel()).setStyleSheet("color: #10b981;")

            # الأنظمة المتقدمة غير متاحة
            for key in ['security', 'permissions', 'backup', 'performance']:
                if key in self.status_labels:
                    self.status_labels[key].setText("⚠️ غير متاح")
                    self.status_labels[key].setStyleSheet("color: #f59e0b;")

    def start_status_monitoring(self):
        """بدء مراقبة حالة الأنظمة"""
        self.status_thread = SystemStatusThread()
        self.status_thread.status_updated.connect(self.update_system_status)
        self.status_thread.start()
    
    def update_system_status(self, status):
        """تحديث حالة الأنظمة"""
        if not status.get('available', False):
            return
        
        initialized = status.get('initialized_systems', [])
        failed = status.get('failed_systems', [])
        
        for system_key, label in self.status_labels.items():
            if system_key in initialized:
                label.setText("✅ يعمل")
                label.setStyleSheet("color: #10b981;")
            elif system_key in failed:
                label.setText("❌ فشل")
                label.setStyleSheet("color: #ef4444;")
            else:
                label.setText("⏳ غير معروف")
                label.setStyleSheet("color: #f59e0b;")
    
    def refresh_system_status(self):
        """تحديث حالة الأنظمة يدوياً"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                status = get_systems_status()
                self.update_system_status(status)
                QMessageBox.information(self, "تحديث الحالة", "✅ تم تحديث حالة الأنظمة")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في تحديث الحالة: {e}")
        elif BASIC_SYSTEMS_AVAILABLE:
            self.show_basic_info()
            QMessageBox.information(self, "تحديث الحالة", "✅ تم تحديث حالة الأنظمة الأساسية")
        else:
            QMessageBox.warning(self, "خطأ", "❌ لا توجد أنظمة متاحة للتحديث")

    def test_systems(self):
        """اختبار الأنظمة"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            QMessageBox.information(self, "اختبار الأنظمة",
                                   "🧪 سيتم تشغيل اختبار شامل للأنظمة...\n"
                                   "💡 يمكنك تشغيل: python test_all_systems.py")
        else:
            QMessageBox.information(self, "اختبار الأنظمة",
                                   "⚠️ الأنظمة المتقدمة غير متاحة\n"
                                   "💡 تثبيت المتطلبات: pip install schedule cryptography bcrypt")

    def restart_systems(self):
        """إعادة تشغيل الأنظمة"""
        reply = QMessageBox.question(self, "إعادة تشغيل الأنظمة",
                                   "⚠️ هل أنت متأكد من إعادة تشغيل جميع الأنظمة؟\n"
                                   "سيتم إيقاف الأنظمة وإعادة تشغيلها.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                if ADVANCED_SYSTEMS_AVAILABLE:
                    from system_initializer import shutdown_advanced_systems, initialize_advanced_systems

                    # إيقاف الأنظمة
                    QMessageBox.information(self, "إعادة التشغيل", "🔄 جاري إيقاف الأنظمة...")
                    shutdown_advanced_systems()

                    # إعادة تشغيل الأنظمة
                    QMessageBox.information(self, "إعادة التشغيل", "🚀 جاري إعادة تشغيل الأنظمة...")
                    success = initialize_advanced_systems()

                    if success:
                        QMessageBox.information(self, "نجح", "✅ تم إعادة تشغيل الأنظمة بنجاح!")
                        self.refresh_system_status()
                    else:
                        QMessageBox.warning(self, "تحذير", "⚠️ تم إعادة تشغيل الأنظمة مع بعض القيود")
                else:
                    QMessageBox.information(self, "إعادة التشغيل", "ℹ️ الأنظمة الأساسية لا تحتاج إعادة تشغيل")

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في إعادة تشغيل الأنظمة: {e}")

    def cleanup_system(self):
        """تنظيف النظام"""
        reply = QMessageBox.question(self, "تنظيف النظام",
                                   "🧹 سيتم تنظيف:\n"
                                   "• السجلات القديمة\n"
                                   "• الملفات المؤقتة\n"
                                   "• ذاكرة التخزين المؤقت\n"
                                   "• النسخ الاحتياطية القديمة\n\n"
                                   "هل تريد المتابعة؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                cleaned_items = []

                # تنظيف السجلات القديمة
                try:
                    if BASIC_SYSTEMS_AVAILABLE:
                        smart_logger.cleanup_old_logs(days_to_keep=7)
                        cleaned_items.append("✅ السجلات القديمة")
                except:
                    cleaned_items.append("⚠️ السجلات القديمة (فشل)")

                # تنظيف الملفات المؤقتة
                try:
                    temp_dir = Path("temp")
                    if temp_dir.exists():
                        import shutil
                        shutil.rmtree(temp_dir)
                        temp_dir.mkdir()
                        cleaned_items.append("✅ الملفات المؤقتة")
                except:
                    cleaned_items.append("⚠️ الملفات المؤقتة (فشل)")

                # تنظيف ذاكرة التخزين المؤقت
                try:
                    cache_dir = Path("cache")
                    if cache_dir.exists():
                        for cache_file in cache_dir.glob("*"):
                            if cache_file.is_file():
                                cache_file.unlink()
                        cleaned_items.append("✅ ذاكرة التخزين المؤقت")
                except:
                    cleaned_items.append("⚠️ ذاكرة التخزين المؤقت (فشل)")

                # تنظيف النسخ الاحتياطية القديمة
                try:
                    if ADVANCED_SYSTEMS_AVAILABLE:
                        backup_manager.cleanup_old_backups(days_to_keep=30)
                        cleaned_items.append("✅ النسخ الاحتياطية القديمة")
                except:
                    cleaned_items.append("⚠️ النسخ الاحتياطية القديمة (فشل)")

                # عرض النتائج
                result_msg = "🧹 نتائج تنظيف النظام:\n\n" + "\n".join(cleaned_items)
                QMessageBox.information(self, "تنظيف النظام", result_msg)

                if BASIC_SYSTEMS_AVAILABLE:
                    log_info("تم تنظيف النظام بنجاح")

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في تنظيف النظام: {e}")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        from PyQt5.QtWidgets import QDialog, QFormLayout, QLineEdit, QDialogButtonBox

        class ChangePasswordDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("🔑 تغيير كلمة المرور")
                self.setFixedSize(400, 250)
                self.setStyleSheet("""
                    QDialog {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #f8fafc, stop:1 #e2e8f0);
                        border-radius: 10px;
                    }
                    QLabel {
                        color: #1e293b;
                        font-weight: bold;
                        font-size: 12px;
                    }
                    QLineEdit {
                        padding: 8px;
                        border: 2px solid #cbd5e1;
                        border-radius: 6px;
                        font-size: 12px;
                        background: white;
                    }
                    QLineEdit:focus {
                        border-color: #3b82f6;
                    }
                    QPushButton {
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                """)

                layout = QFormLayout()

                self.current_password = QLineEdit()
                self.current_password.setEchoMode(QLineEdit.Password)
                self.current_password.setPlaceholderText("أدخل كلمة المرور الحالية")

                self.new_password = QLineEdit()
                self.new_password.setEchoMode(QLineEdit.Password)
                self.new_password.setPlaceholderText("أدخل كلمة المرور الجديدة")

                self.confirm_password = QLineEdit()
                self.confirm_password.setEchoMode(QLineEdit.Password)
                self.confirm_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")

                layout.addRow("🔒 كلمة المرور الحالية:", self.current_password)
                layout.addRow("🔑 كلمة المرور الجديدة:", self.new_password)
                layout.addRow("✅ تأكيد كلمة المرور:", self.confirm_password)

                buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
                buttons.accepted.connect(self.accept)
                buttons.rejected.connect(self.reject)
                layout.addRow(buttons)

                self.setLayout(layout)

        dialog = ChangePasswordDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            current = dialog.current_password.text()
            new_pass = dialog.new_password.text()
            confirm = dialog.confirm_password.text()

            if not current or not new_pass or not confirm:
                QMessageBox.warning(self, "خطأ", "❌ يرجى ملء جميع الحقول")
                return

            if new_pass != confirm:
                QMessageBox.warning(self, "خطأ", "❌ كلمة المرور الجديدة غير متطابقة")
                return

            if len(new_pass) < 6:
                QMessageBox.warning(self, "خطأ", "❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return

            try:
                if ADVANCED_SYSTEMS_AVAILABLE:
                    # تشفير كلمة المرور الجديدة
                    hashed_password = security_manager.hash_password(new_pass)
                    # حفظ في قاعدة البيانات أو ملف الإعدادات
                    QMessageBox.information(self, "نجح", "✅ تم تغيير كلمة المرور بنجاح!")
                    if BASIC_SYSTEMS_AVAILABLE:
                        log_info("تم تغيير كلمة المرور بنجاح")
                else:
                    # حفظ بسيط بدون تشفير متقدم
                    QMessageBox.information(self, "نجح", "✅ تم تغيير كلمة المرور بنجاح!")

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في تغيير كلمة المرور: {e}")
    
    def show_security_report(self):
        """عرض تقرير الأمان"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                report = security_manager.get_security_report()
                msg = f"""📋 تقرير الأمان:
                
🔍 الأنشطة المشبوهة: {report.get('total_suspicious_activities', 0)}
🚫 عناوين IP المحظورة: {report.get('blocked_ips_count', 0)}
📊 الأنشطة الأخيرة (24 ساعة): {report.get('recent_activities_24h', 0)}
"""
                QMessageBox.information(self, "تقرير الأمان", msg)
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في تقرير الأمان: {e}")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                backup_info = backup_manager.create_backup("full", compress=True)
                if backup_info:
                    QMessageBox.information(self, "النسخ الاحتياطي", 
                                          f"✅ تم إنشاء النسخة الاحتياطية:\n{backup_info['filename']}")
                else:
                    QMessageBox.warning(self, "خطأ", "❌ فشل في إنشاء النسخة الاحتياطية")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في النسخ الاحتياطي: {e}")
    
    def list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                backups = backup_manager.get_backup_list()
                msg = f"📋 النسخ الاحتياطية المتاحة: {len(backups)}\n\n"
                for backup in backups[:5]:  # أحدث 5 نسخ
                    msg += f"📁 {backup['filename']}\n"
                QMessageBox.information(self, "النسخ الاحتياطية", msg)
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في قائمة النسخ: {e}")
    
    def show_performance_report(self):
        """عرض تقرير الأداء"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                report = performance_monitor.get_performance_report(24)
                msg = f"""📊 تقرير الأداء (24 ساعة):
                
📈 نقاط البيانات: {report.get('data_points', 0)}
⚠️ التنبيهات: {report.get('alerts_count', 0)}
🐌 الاستعلامات البطيئة: {report.get('slow_queries_count', 0)}
⏱️ وقت التشغيل: {report.get('uptime_seconds', 0):.0f} ثانية
"""
                QMessageBox.information(self, "تقرير الأداء", msg)
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في تقرير الأداء: {e}")
    
    def optimize_performance(self):
        """تحسين الأداء"""
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                performance_monitor.optimize_performance()
                QMessageBox.information(self, "تحسين الأداء", "⚡ تم تشغيل تحسين الأداء بنجاح!")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في تحسين الأداء: {e}")
    
    def view_logs(self):
        """عرض السجلات"""
        try:
            logs_dir = Path("logs")
            if logs_dir.exists():
                log_files = list(logs_dir.glob("*.log"))
                if log_files:
                    # قراءة آخر 20 سطر من السجل الرئيسي
                    main_log = logs_dir / "application.log"
                    if main_log.exists():
                        with open(main_log, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            recent_lines = lines[-20:] if len(lines) > 20 else lines
                            self.logs_text.setText(''.join(recent_lines))
                else:
                    self.logs_text.setText("📝 لا توجد ملفات سجلات")
            else:
                self.logs_text.setText("📁 مجلد السجلات غير موجود")
        except Exception as e:
            self.logs_text.setText(f"❌ خطأ في قراءة السجلات: {e}")
    
    def clear_logs(self):
        """مسح السجلات"""
        reply = QMessageBox.question(self, "مسح السجلات", 
                                   "⚠️ هل أنت متأكد من مسح جميع السجلات؟",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                logs_dir = Path("logs")
                if logs_dir.exists():
                    for log_file in logs_dir.glob("*.log"):
                        log_file.unlink()
                    QMessageBox.information(self, "مسح السجلات", "✅ تم مسح السجلات بنجاح")
                    self.logs_text.clear()
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في مسح السجلات: {e}")
    
    def export_logs(self):
        """تصدير السجلات"""
        from PyQt5.QtWidgets import QFileDialog
        import shutil
        import zipfile
        from datetime import datetime

        try:
            # اختيار مكان الحفظ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"logs_export_{timestamp}.zip"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "💾 حفظ السجلات المُصدرة",
                default_filename,
                "ملفات مضغوطة (*.zip);;جميع الملفات (*.*)"
            )

            if not file_path:
                return

            logs_dir = Path("logs")
            if not logs_dir.exists():
                QMessageBox.warning(self, "خطأ", "❌ مجلد السجلات غير موجود")
                return

            log_files = list(logs_dir.glob("*.log"))
            if not log_files:
                QMessageBox.warning(self, "خطأ", "❌ لا توجد ملفات سجلات للتصدير")
                return

            # إنشاء ملف مضغوط
            with zipfile.ZipFile(file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for log_file in log_files:
                    zipf.write(log_file, log_file.name)

                # إضافة ملف معلومات التصدير
                export_info = f"""📋 معلومات التصدير

🕐 تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📁 عدد الملفات: {len(log_files)}
📊 الملفات المُصدرة:
"""
                for log_file in log_files:
                    size_kb = log_file.stat().st_size / 1024
                    export_info += f"   • {log_file.name} ({size_kb:.1f} KB)\n"

                zipf.writestr("export_info.txt", export_info)

            QMessageBox.information(self, "نجح التصدير",
                                  f"✅ تم تصدير السجلات بنجاح!\n"
                                  f"📁 الملف: {Path(file_path).name}\n"
                                  f"📊 عدد الملفات: {len(log_files)}")

            if BASIC_SYSTEMS_AVAILABLE:
                log_info(f"تم تصدير السجلات إلى: {file_path}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"❌ خطأ في تصدير السجلات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ الإعدادات في قاعدة البيانات أو ملف
            QMessageBox.information(self, "حفظ الإعدادات", "✅ تم حفظ الإعدادات بنجاح!")
            log_info("تم حفظ إعدادات المستخدم")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"❌ خطأ في حفظ الإعدادات: {e}")
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(self, "إعادة تعيين", 
                                   "⚠️ هل أنت متأكد من إعادة تعيين جميع الإعدادات؟",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                # إعادة تعيين الإعدادات للقيم الافتراضية
                self.min_password_length.setValue(4)
                self.max_login_attempts.setValue(5)
                self.session_timeout.setValue(3600)
                self.auto_backup_enabled.setChecked(True)
                self.backup_interval.setValue(6)
                self.max_backups.setValue(30)
                self.backup_compression.setChecked(True)
                self.performance_monitoring.setChecked(True)
                self.max_memory.setValue(512)
                self.max_query_time.setValue(5)
                
                QMessageBox.information(self, "إعادة تعيين", "✅ تم إعادة تعيين الإعدادات")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"❌ خطأ في إعادة التعيين: {e}")

    def check_for_updates(self):
        """فحص التحديثات"""
        try:
            if BASIC_SYSTEMS_AVAILABLE:
                from config import AppConfig
                current_version = getattr(AppConfig, 'VERSION', '1.0.0')
            else:
                current_version = '1.0.0'

            # محاكاة فحص التحديثات
            import random
            has_update = random.choice([True, False])

            if has_update:
                new_version = "1.1.0"
                reply = QMessageBox.question(self, "تحديث متاح",
                                           f"🎉 يتوفر تحديث جديد!\n\n"
                                           f"📊 الإصدار الحالي: {current_version}\n"
                                           f"🆕 الإصدار الجديد: {new_version}\n\n"
                                           f"الميزات الجديدة:\n"
                                           f"• تحسينات في الأداء\n"
                                           f"• إصلاح الأخطاء\n"
                                           f"• ميزات جديدة\n\n"
                                           f"هل تريد تحديث التطبيق؟",
                                           QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    QMessageBox.information(self, "تم التحديث",
                                          f"✅ تم تحديث التطبيق بنجاح!\n"
                                          f"🎉 الإصدار الجديد: {new_version}\n\n"
                                          f"يرجى إعادة تشغيل التطبيق لتطبيق التحديثات.")
            else:
                QMessageBox.information(self, "لا توجد تحديثات",
                                      f"✅ التطبيق محدث!\n\n"
                                      f"📊 الإصدار الحالي: {current_version}\n"
                                      f"🎯 لا توجد تحديثات متاحة حالياً.")

            if BASIC_SYSTEMS_AVAILABLE:
                log_info("تم فحص التحديثات")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"❌ خطأ في فحص التحديثات: {e}")

    def closeEvent(self, event):
        """إغلاق النافذة"""
        if self.status_thread:
            self.status_thread.terminate()
            self.status_thread.wait()
        event.accept()
