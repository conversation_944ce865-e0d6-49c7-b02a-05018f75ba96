2025-07-20 20:28:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:28:16.423125",
  "exception_type": "AttributeError",
  "exception_message": "'MainWindow' object has no attribute 'switch_to_settings_tab'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:28:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:28:18.447073",
  "exception_type": "AttributeError",
  "exception_message": "'MainWindow' object has no attribute 'switch_to_settings_tab'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:38.604736",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:42.465801",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:47.570551",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:35:18.778858",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:35:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:35:25.331454",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:36:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:36:46.796622",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:36:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:36:51.026833",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:37:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:37:29.153097",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:24.034451",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:41.052459",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:54.477297",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:12.230249",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:30.422753",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:43.878653",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:05.687420",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:24.308970",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:32.036438",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:49.374662",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:59.224712",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:27.930876",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:40.293662",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:56.000652",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:28.772550",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:41.165362",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:56.133222",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:23.470992",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:42.600129",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:55.516541",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:12.630932",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:51.455145",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:57 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:57.949384",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:45:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:45:26.129689",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:06.161827",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:14.690761",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:27.696224",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:04.722971",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:15.747765",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:46.406420",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:48:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:48:25.472376",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:49:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:49:32.016816",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:49:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:49:38.918611",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:50:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:50:35.479953",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:34.237563",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:37.449209",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:40.760512",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:47.701055",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:38.872518",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:42.189800",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:48.869030",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:19.045980",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:22.300085",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:30.530878",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:52.346150",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:53.339244",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:53.344665",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:54.352718",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:54.354591",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:55.609051",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:39.557932",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:40.667440",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:40.674256",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:41.679170",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:41.680475",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:01.671946",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:02.768555",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:02.775345",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:03.801996",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:03.803601",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:45.797863",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:46.811812",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:46.817736",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:47.823533",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:47.824823",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:18.995121",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:20.028548",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:20.034929",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:21.047110",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:21.048650",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:58.120604",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:59.112634",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:59.119255",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:58:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:58:00.126098",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:58:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:58:00.127326",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:20.865261",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:21.866840",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:21.872426",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:22.880680",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:22.882166",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:39.575635",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:39.603955",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:40.615397",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:40.616523",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:16.194466",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:17.236053",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:17.241920",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:18.247213",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:18.259181",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:03:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:03:51.957263",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:05.024371",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:25.597923",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:26.712177",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:26.722370",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:27.729546",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:27.731248",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:19.180438",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:20.190454",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:20.196737",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:21.216093",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:21.217337",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:47.156594",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:10:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:10:29.912673",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:16.129143",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:18.786837",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:19.802276",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:19.808453",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:20.819747",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:20.821114",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:21.836075",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:21.837271",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:26.420825",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:38.199375",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:39.245988",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:39.252367",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:40.258805",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:40.260197",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:59.344303",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:10.042967",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:11.548399",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:11.564402",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:12.752363",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:12.754789",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:43.050577",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:47.557187",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحسين الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 377, in optimize_performance\n    smart_logger.log_info(f\"تم تنظيف {collected} كائن من الذاكرة\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:55.078209",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:04.175161",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:05.195901",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:05.201433",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:06.332662",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:06.333999",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:17.268404",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:36.396893",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:36.404652",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:37.385753",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:37.391689",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:38.422036",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:38.423401",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:34.792877",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:39.438499",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:39.440039",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:18:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:18:40.452287",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:18:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:18:40.453242",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:30.730488",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء النسخة الاحتياطية",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 62, in create_backup\n    smart_logger.log_info(f\"بدء إنشاء نسخة احتياطية من نوع: {backup_type}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:41.466207",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:41.467246",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:42.475441",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:42.476400",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:49.434500",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:03.612338",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:07.337125",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحسين الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 377, in optimize_performance\n    smart_logger.log_info(f\"تم تنظيف {collected} كائن من الذاكرة\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:43.489627",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:43.490590",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:22:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:22:19.115928",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
