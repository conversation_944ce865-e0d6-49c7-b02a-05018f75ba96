2025-07-20 20:28:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:28:16.423125",
  "exception_type": "AttributeError",
  "exception_message": "'MainWindow' object has no attribute 'switch_to_settings_tab'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:28:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:28:18.447073",
  "exception_type": "AttributeError",
  "exception_message": "'MainWindow' object has no attribute 'switch_to_settings_tab'",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:38.604736",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:42.465801",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:34:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:34:47.570551",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:35:18.778858",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:35:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:35:25.331454",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:36:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:36:46.796622",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:36:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:36:51.026833",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:37:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:37:29.153097",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:24.034451",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:41.052459",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:38:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:38:54.477297",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:12.230249",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:30.422753",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:39:43.878653",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:05.687420",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:24.308970",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:32.036438",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:49.374662",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:40:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:40:59.224712",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:27.930876",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:40.293662",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:41:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:41:56.000652",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:28 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:28.772550",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:41.165362",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:42:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:42:56.133222",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:23.470992",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:42.600129",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:43:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:43:55.516541",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:12.630932",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:51.455145",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:44:57 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:44:57.949384",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:45:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:45:26.129689",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:06.161827",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:14.690761",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:46:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:46:27.696224",
  "exception_type": "NameError",
  "exception_message": "name 'List' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:04.722971",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:15.747765",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:47:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:47:46.406420",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:48:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:48:25.472376",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:49:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:49:32.016816",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:49:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:49:38.918611",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:50:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:50:35.479953",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:34.237563",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:37.449209",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:40.760512",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:51:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:51:47.701055",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:38.872518",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:42.189800",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:52:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:52:48.869030",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:19.045980",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:22.300085",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:30.530878",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:52.346150",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:53.339244",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:53.344665",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:54.352718",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:54.354591",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:53:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:53:55.609051",
  "exception_type": "NameError",
  "exception_message": "name 'search_layout' is not defined",
  "context": "Unhandled Exception",
  "traceback": "NoneType: None\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:39.557932",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:40.667440",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:40.674256",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:41.679170",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:54:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:54:41.680475",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:01.671946",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:02.768555",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:02.775345",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:03.801996",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:03.803601",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:45.797863",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:46.811812",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:46.817736",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:47.823533",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:55:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:55:47.824823",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:18.995121",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:20.028548",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:20.034929",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:21.047110",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:21.048650",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:58.120604",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:59.112634",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:57:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:57:59.119255",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:58:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:58:00.126098",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:58:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:58:00.127326",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:20.865261",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:21.866840",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:21.872426",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:22.880680",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 20:59:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T20:59:22.882166",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:39.575635",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:39.603955",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:40.615397",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:00:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:00:40.616523",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:16.194466",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:17.236053",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:17.241920",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:18.247213",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:01:18.259181",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:03:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:03:51.957263",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:05.024371",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:25.597923",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:26.712177",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:26.722370",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:27.729546",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:04:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:04:27.731248",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:19.180438",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:20.190454",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:20.196737",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:21.216093",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:21.217337",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:07:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:07:47.156594",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:10:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:10:29.912673",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:16.129143",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:18.786837",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:19.802276",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:19.808453",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:20.819747",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:11:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:11:20.821114",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:21.836075",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:21.837271",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:12:26 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:12:26.420825",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:38.199375",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:39.245988",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:39.252367",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:40.258805",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:40.260197",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:14:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:14:59.344303",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:10.042967",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:11.548399",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:11.564402",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:12.752363",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:12.754789",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:43.050577",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:47.557187",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحسين الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 377, in optimize_performance\n    smart_logger.log_info(f\"تم تنظيف {collected} كائن من الذاكرة\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:15:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:15:55.078209",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:04 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:04.175161",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:05.195901",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:05 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:05.201433",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:06.332662",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:06 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:06.333999",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:17.268404",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:36.396893",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:36.404652",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:37.385753",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:37.391689",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:38.422036",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:16:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:16:38.423401",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:34.792877",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:39.438499",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:17:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:17:39.440039",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:18:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:18:40.452287",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:18:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:18:40.453242",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:30.730488",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء النسخة الاحتياطية",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 62, in create_backup\n    smart_logger.log_info(f\"بدء إنشاء نسخة احتياطية من نوع: {backup_type}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:41.466207",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:19:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:19:41.467246",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:42.475441",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:42.476400",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:20:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:20:49.434500",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:03 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:03.612338",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في إنشاء تقرير الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 323, in get_performance_report\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:07 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:07.337125",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحسين الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 377, in optimize_performance\n    smart_logger.log_info(f\"تم تنظيف {collected} كائن من الذاكرة\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:43.489627",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:21:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:21:43.490590",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:22:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:22:19.115928",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:23:56 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:23:56.577132",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:40.758872",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:41.860658",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:41.866620",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:42.979149",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:24:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:24:42.980493",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:25:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:25:43.996212",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:25:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:25:43.997210",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:14.366020",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:19.994659",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:21.043249",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:21.048708",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:22.182500",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:26:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:26:22.185188",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:22.188291",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:37 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:37.258503",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:38.265667",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:38 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:38.271202",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:39.364470",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:27:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:27:39.365825",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:39 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:39.369324",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:43.268625",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:44.267574",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:44.273839",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:45.411667",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:28:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:28:45.412932",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:19.893200",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:23 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:23.087390",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:24.087518",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:24 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:24.093221",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:25.732282",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:29:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:29:25.746679",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:02 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:02.609819",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:10.750636",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:12 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:12.109512",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:13.103308",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:13.108879",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:14.215642",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:30:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:30:14.216976",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:15.228052",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:15.229161",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:25 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:25.266721",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:29.494549",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:30.529137",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:30.535441",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:31.556617",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:31.558277",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:52.179316",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:53.191851",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:53 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:53.197463",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:54.223062",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:31:54 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:31:54.224392",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:17.361763",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:19.247591",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:20.244126",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:20.250239",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:21.264350",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:32:21 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:32:21.268220",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:22.285525",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:22 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:22.288036",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:44.222513",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:49.084431",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:50 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:50.179201",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:50 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:50.184604",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:51.208449",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:33:51 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:33:51.209679",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:09.428150",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:15.056452",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:16.070011",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:16.076132",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:17.121262",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:34:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:34:17.122722",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:35:18.140415",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:35:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:35:18.143483",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:35:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:35:59.574020",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:36:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:36:19.160995",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:36:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:36:19.163330",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:36:36 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:36:36.265080",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:17.043445",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:40 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:40.397177",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:41.444628",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:41.450282",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:42.651014",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:37:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:37:42.652537",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:19.170639",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:43.669695",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:43.672984",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:55 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:55.373398",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:38:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:38:59.778602",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:00.799378",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:00.806201",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:01.844179",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:01.846537",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:41 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:41.595061",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:42.590913",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:42.596779",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:43.618339",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:39:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:39:43.619661",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:09 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:09.584895",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:10.648807",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:10.654622",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:11.671885",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:11 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:11.673354",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:27 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:27.934969",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:29.555055",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:30.556078",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:30.562095",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:31.569693",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:40:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:40:31.571163",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:32.584804",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:32.586137",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:42 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:42.487392",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:47.679358",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:48.678513",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:48.684384",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:49.823337",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:41:49 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:41:49.824892",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:10 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:10.405277",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:14.672050",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:15.686807",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:15.693410",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:16.770137",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:16.771610",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:43.119493",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:44.096872",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:44 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:44.103426",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:45.129527",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:42:45 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:42:45.131275",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:43:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:43:46.150076",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:43:46 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:43:46.153038",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:44:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:44:47.170983",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:44:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:44:47.174201",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:45:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:45:48.184622",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:45:48 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:45:48.185608",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:45:52 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:45:52.856907",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:48:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:48:58.307054",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:48:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:48:59.368097",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:48:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:48:59.374208",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:00.402296",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:00.403617",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:18.187612",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"E:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - E:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:49:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:49:20.894689",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:13 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:13.087685",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:14.117332",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:14 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:14.123232",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:15.243560",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:50:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:50:15.244834",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:51:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:51:16.255470",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:51:16 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:51:16.256819",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:51:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:51:20.861969",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:53:58 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:53:58.080523",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:53:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:53:59.103369",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:53:59 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:53:59.108947",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:54:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:54:00.128136",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:54:00 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:54:00.130749",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:54:43 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:54:43.816849",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء النسخة الاحتياطية",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 62, in create_backup\n    smart_logger.log_info(f\"بدء إنشاء نسخة احتياطية من نوع: {backup_type}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:55:01.146488",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:55:01 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:55:01.147513",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:55:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:55:20.664394",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:17 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:17.586764",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:18.598716",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:18.603794",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:19.688673",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:57:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:57:19.689943",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:58:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:58:20.707889",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:58:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:58:20.711209",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 21:59:15 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T21:59:15.531614",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:33 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:33.495407",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:34.532822",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:34 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:34.539164",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:35.569617",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:35 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:35.571121",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:00:47 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:00:47.253931",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:18 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:18.713496",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:19.724059",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:19 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:19.729677",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:20.767178",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:20 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:20.768443",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:01:30 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:01:30.834248",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:29 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:29.616835",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في تحميل مفتاح التشفير",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 94, in load_or_create_encryption_key\n    smart_logger.log_info(\"تم تحميل مفتاح التشفير بنجاح\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:31.174922",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء النسخ الاحتياطي التلقائي",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\backup_manager.py\", line 332, in start_automatic_backup\n    smart_logger.log_info(\"تم بدء النسخ الاحتياطي التلقائي\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:31 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:31.191531",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في بدء مراقبة الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 48, in start_monitoring\n    smart_logger.log_info(\"تم بدء مراقبة الأداء\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:32.461898",
  "exception_type": "TypeError",
  "exception_message": "unsupported operand type(s) for +: 'int' and 'dict'",
  "context": "خطأ في جمع بيانات الأداء",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 122, in _collect_performance_data\n    \"gc_collections\": sum(gc.get_stats()),\n                      ~~~^^^^^^^^^^^^^^^^\nTypeError: unsupported operand type(s) for +: 'int' and 'dict'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:18:32 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:18:32.464840",
  "exception_type": "KeyError",
  "exception_message": "'timestamp'",
  "context": "خطأ في تنظيف البيانات القديمة",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\performance_monitor.py\", line 241, in _cleanup_old_data\n    if datetime.fromisoformat(data[\"timestamp\"]) > cutoff_time\n                              ~~~~^^^^^^^^^^^^^\nKeyError: 'timestamp'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
2025-07-20 22:19:08 - ERROR - ERROR - Exception occurred: {
  "timestamp": "2025-07-20T22:19:08.132079",
  "exception_type": "AttributeError",
  "exception_message": "'SmartLogger' object has no attribute 'log_info'",
  "context": "خطأ في إنشاء نسخة احتياطية لبيانات الأمان",
  "traceback": "Traceback (most recent call last):\n  File \"e:\\3.Python\\3.Place of experiments\\4.Place to create the program\\security_manager.py\", line 398, in backup_security_data\n    smart_logger.log_info(f\"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}\")\n    ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'SmartLogger' object has no attribute 'log_info'\n"
} - e:\3.Python\3.Place of experiments\4.Place to create the program\logging_config.py:223
