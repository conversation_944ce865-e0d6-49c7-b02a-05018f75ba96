# -*- coding: utf-8 -*-
"""
نظام إدارة الأمان والتشفير المتقدم
Advanced Security and Encryption Management System
"""

import hashlib
import secrets
import base64
import os
import re
from typing import Optional, Dict, Tuple
import json
from datetime import datetime, timedelta

# استيراد اختياري للمكتبات المتقدمة
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
    print("⚠️ مكتبة cryptography غير متاحة - التشفير المتقدم معطل")

try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False
    print("⚠️ مكتبة bcrypt غير متاحة - تشفير كلمات المرور معطل")

from config import SecurityConfig, AppConfig
from logging_config import smart_logger

class SecurityManager:
    """مدير الأمان والتشفير المتقدم"""
    
    def __init__(self):
        self.encryption_key = None
        self.load_or_create_encryption_key()
        self.security_policies = self._initialize_security_policies()
        self.blocked_ips = set()
        self.suspicious_activities = []
    
    def _initialize_security_policies(self) -> Dict:
        """تهيئة سياسات الأمان"""
        return {
            "password_policy": {
                "min_length": SecurityConfig.PASSWORD_MIN_LENGTH,
                "require_uppercase": False,  # للعربية
                "require_lowercase": False,  # للعربية
                "require_numbers": True,
                "require_special_chars": False,
                "max_age_days": 90,
                "history_count": 5
            },
            "session_policy": {
                "timeout_seconds": SecurityConfig.SESSION_TIMEOUT,
                "max_concurrent_sessions": 3,
                "require_secure_connection": False
            },
            "access_policy": {
                "max_login_attempts": SecurityConfig.MAX_LOGIN_ATTEMPTS,
                "lockout_duration": SecurityConfig.LOCKOUT_DURATION,
                "ip_whitelist_enabled": False,
                "geo_blocking_enabled": False
            }
        }
    
    def load_or_create_encryption_key(self):
        """تحميل أو إنشاء مفتاح التشفير"""
        if not CRYPTOGRAPHY_AVAILABLE:
            self.encryption_key = None
            return

        try:
            key_file = AppConfig.DATA_DIR / "encryption.key"
            AppConfig.DATA_DIR.mkdir(exist_ok=True)

            if key_file.exists():
                # تحميل المفتاح الموجود
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # إنشاء مفتاح جديد
                self.encryption_key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)

                # تعيين صلاحيات الملف (قراءة فقط للمالك)
                os.chmod(key_file, 0o600)

            smart_logger.log_info("تم تحميل مفتاح التشفير بنجاح")

        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تحميل مفتاح التشفير")
            # إنشاء مفتاح مؤقت في الذاكرة
            if CRYPTOGRAPHY_AVAILABLE:
                self.encryption_key = Fernet.generate_key()
            else:
                self.encryption_key = None
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور باستخدام bcrypt"""
        if not BCRYPT_AVAILABLE:
            # استخدام تشفير بديل بسيط
            return hashlib.sha256(password.encode('utf-8')).hexdigest()

        try:
            # تحويل كلمة المرور إلى bytes
            password_bytes = password.encode('utf-8')

            # إنشاء hash
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password_bytes, salt)

            return hashed.decode('utf-8')

        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تشفير كلمة المرور")
            # استخدام تشفير بديل
            return hashlib.sha256(password.encode('utf-8')).hexdigest()
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """التحقق من كلمة المرور"""
        if not BCRYPT_AVAILABLE:
            # استخدام التحقق البديل البسيط
            return hashlib.sha256(password.encode('utf-8')).hexdigest() == hashed_password

        try:
            # محاولة التحقق باستخدام bcrypt أولاً
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')

            return bcrypt.checkpw(password_bytes, hashed_bytes)

        except Exception as e:
            # في حالة الفشل، جرب التحقق البديل
            return hashlib.sha256(password.encode('utf-8')).hexdigest() == hashed_password
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """التحقق من قوة كلمة المرور"""
        errors = []
        policy = self.security_policies["password_policy"]
        
        # طول كلمة المرور
        if len(password) < policy["min_length"]:
            errors.append(f"كلمة المرور يجب أن تكون {policy['min_length']} أحرف على الأقل")
        
        # وجود أرقام
        if policy["require_numbers"] and not re.search(r'\d', password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        # وجود أحرف خاصة
        if policy["require_special_chars"] and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        # كلمات المرور الضعيفة الشائعة
        weak_passwords = ['123456', 'password', 'admin', '123123', 'qwerty']
        if password.lower() in weak_passwords:
            errors.append("كلمة المرور ضعيفة جداً، يرجى اختيار كلمة مرور أقوى")
        
        return len(errors) == 0, errors
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        if not CRYPTOGRAPHY_AVAILABLE or not self.encryption_key:
            # استخدام تشفير بديل بسيط
            return base64.b64encode(data.encode('utf-8')).decode('utf-8')

        try:
            fernet = Fernet(self.encryption_key)
            encrypted_data = fernet.encrypt(data.encode('utf-8'))

            return base64.b64encode(encrypted_data).decode('utf-8')

        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تشفير البيانات")
            # استخدام تشفير بديل
            return base64.b64encode(data.encode('utf-8')).decode('utf-8')
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        if not CRYPTOGRAPHY_AVAILABLE or not self.encryption_key:
            # استخدام فك تشفير بديل بسيط
            try:
                return base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
            except:
                return encrypted_data

        try:
            fernet = Fernet(self.encryption_key)
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = fernet.decrypt(encrypted_bytes)

            return decrypted_data.decode('utf-8')

        except Exception as e:
            # محاولة فك التشفير البديل
            try:
                return base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
            except:
                return encrypted_data
    
    def generate_secure_token(self, length: int = 32) -> str:
        """إنشاء رمز آمن عشوائي"""
        try:
            return secrets.token_urlsafe(length)
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء الرمز الآمن")
            return ""
    
    def sanitize_input(self, input_data: str) -> str:
        """تنظيف المدخلات من الأكواد الضارة"""
        try:
            # إزالة الأكواد الضارة الأساسية
            dangerous_patterns = [
                r'<script.*?>.*?</script>',  # JavaScript
                r'<.*?on\w+\s*=.*?>',       # Event handlers
                r'javascript:',              # JavaScript URLs
                r'vbscript:',               # VBScript URLs
                r'data:',                   # Data URLs
                r'<iframe.*?>.*?</iframe>', # iframes
            ]
            
            cleaned_data = input_data
            for pattern in dangerous_patterns:
                cleaned_data = re.sub(pattern, '', cleaned_data, flags=re.IGNORECASE | re.DOTALL)
            
            # تنظيف SQL injection الأساسي
            sql_patterns = [
                r"'.*?--",
                r'".*?--',
                r';.*?drop\s+table',
                r';.*?delete\s+from',
                r';.*?insert\s+into',
                r';.*?update\s+.*?set'
            ]
            
            for pattern in sql_patterns:
                cleaned_data = re.sub(pattern, '', cleaned_data, flags=re.IGNORECASE)
            
            return cleaned_data.strip()
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تنظيف المدخلات")
            return input_data
    
    def validate_sql_query(self, query: str) -> bool:
        """التحقق من أمان استعلام SQL"""
        try:
            # قائمة الكلمات المحظورة
            dangerous_keywords = [
                'drop', 'delete', 'truncate', 'alter', 'create',
                'exec', 'execute', 'sp_', 'xp_', '--', '/*', '*/'
            ]
            
            query_lower = query.lower()
            
            for keyword in dangerous_keywords:
                if keyword in query_lower:
                    smart_logger.log_security_event("sql_injection_attempt", {
                        "query": query[:100],  # أول 100 حرف فقط
                        "dangerous_keyword": keyword
                    })
                    return False
            
            return True
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في التحقق من استعلام SQL")
            return False
    
    def log_suspicious_activity(self, activity_type: str, details: Dict, severity: str = "medium"):
        """تسجيل نشاط مشبوه"""
        try:
            activity = {
                "timestamp": datetime.now().isoformat(),
                "type": activity_type,
                "details": details,
                "severity": severity
            }
            
            self.suspicious_activities.append(activity)
            
            # الاحتفاظ بآخر 1000 نشاط فقط
            if len(self.suspicious_activities) > 1000:
                self.suspicious_activities = self.suspicious_activities[-1000:]
            
            # تسجيل في نظام السجلات
            smart_logger.log_security_event(f"suspicious_activity_{activity_type}", details, severity.upper())
            
            # إجراءات تلقائية للأنشطة عالية الخطورة
            if severity == "high":
                self._handle_high_risk_activity(activity)
                
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تسجيل النشاط المشبوه")
    
    def _handle_high_risk_activity(self, activity: Dict):
        """التعامل مع الأنشطة عالية الخطورة"""
        try:
            # يمكن إضافة إجراءات مثل:
            # - حظر IP
            # - إرسال تنبيه
            # - قفل الحساب
            
            smart_logger.log_critical(f"نشاط عالي الخطورة: {activity['type']}")
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في التعامل مع النشاط عالي الخطورة")
    
    def block_ip(self, ip_address: str, reason: str = ""):
        """حظر عنوان IP"""
        try:
            self.blocked_ips.add(ip_address)
            
            self.log_suspicious_activity("ip_blocked", {
                "ip_address": ip_address,
                "reason": reason
            }, "high")
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في حظر عنوان IP")
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """التحقق من حظر عنوان IP"""
        return ip_address in self.blocked_ips
    
    def unblock_ip(self, ip_address: str):
        """إلغاء حظر عنوان IP"""
        try:
            self.blocked_ips.discard(ip_address)
            smart_logger.log_info(f"تم إلغاء حظر عنوان IP: {ip_address}")
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إلغاء حظر عنوان IP")
    
    def get_security_report(self) -> Dict:
        """الحصول على تقرير الأمان"""
        try:
            # إحصائيات الأنشطة المشبوهة
            activity_stats = {}
            for activity in self.suspicious_activities:
                activity_type = activity['type']
                activity_stats[activity_type] = activity_stats.get(activity_type, 0) + 1
            
            # الأنشطة الأخيرة (آخر 24 ساعة)
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_activities = [
                activity for activity in self.suspicious_activities
                if datetime.fromisoformat(activity['timestamp']) > recent_cutoff
            ]
            
            return {
                "total_suspicious_activities": len(self.suspicious_activities),
                "recent_activities_24h": len(recent_activities),
                "blocked_ips_count": len(self.blocked_ips),
                "activity_breakdown": activity_stats,
                "security_policies": self.security_policies,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء تقرير الأمان")
            return {}
    
    def update_security_policy(self, policy_name: str, policy_data: Dict):
        """تحديث سياسة الأمان"""
        try:
            if policy_name in self.security_policies:
                self.security_policies[policy_name].update(policy_data)
                
                smart_logger.log_security_event("security_policy_updated", {
                    "policy_name": policy_name,
                    "changes": policy_data
                })
                
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تحديث سياسة الأمان")
    
    def backup_security_data(self) -> bool:
        """نسخ احتياطي لبيانات الأمان"""
        try:
            security_data = {
                "blocked_ips": list(self.blocked_ips),
                "suspicious_activities": self.suspicious_activities,
                "security_policies": self.security_policies,
                "backup_timestamp": datetime.now().isoformat()
            }
            
            backup_file = AppConfig.BACKUP_DIR / f"security_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            AppConfig.BACKUP_DIR.mkdir(exist_ok=True)
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(security_data, f, ensure_ascii=False, indent=2)
            
            smart_logger.log_info(f"تم إنشاء نسخة احتياطية لبيانات الأمان: {backup_file.name}")
            return True
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء نسخة احتياطية لبيانات الأمان")
            return False

# إنشاء مثيل عام من مدير الأمان
security_manager = SecurityManager()

# دوال مساعدة سريعة
def hash_password(password: str) -> str:
    """تشفير كلمة مرور سريع"""
    return security_manager.hash_password(password)

def verify_password(password: str, hashed: str) -> bool:
    """التحقق من كلمة مرور سريع"""
    return security_manager.verify_password(password, hashed)

def encrypt_data(data: str) -> str:
    """تشفير بيانات سريع"""
    return security_manager.encrypt_data(data)

def decrypt_data(encrypted_data: str) -> str:
    """فك تشفير بيانات سريع"""
    return security_manager.decrypt_data(encrypted_data)

def sanitize_input(input_data: str) -> str:
    """تنظيف مدخلات سريع"""
    return security_manager.sanitize_input(input_data)

if __name__ == "__main__":
    # اختبار نظام الأمان
    print("🔧 اختبار نظام الأمان...")
    
    # اختبار تشفير كلمة المرور
    password = "test123"
    hashed = hash_password(password)
    if verify_password(password, hashed):
        print("✅ تشفير والتحقق من كلمة المرور يعمل بنجاح")
    
    # اختبار تشفير البيانات
    data = "بيانات سرية للاختبار"
    encrypted = encrypt_data(data)
    decrypted = decrypt_data(encrypted)
    if decrypted == data:
        print("✅ تشفير وفك تشفير البيانات يعمل بنجاح")
    
    # اختبار تنظيف المدخلات
    dangerous_input = "<script>alert('hack')</script>بيانات عادية"
    cleaned = sanitize_input(dangerous_input)
    if "<script>" not in cleaned:
        print("✅ تنظيف المدخلات يعمل بنجاح")
    
    print("✅ تم اختبار نظام الأمان بنجاح")
